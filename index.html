<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON>ra <PERSON>zwi reZhangwa</title>
    <link rel="stylesheet" href="style.css" />
    <!-- Add libraries here -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.4/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- AI Integration Scripts -->
    <script src="config.js"></script>
    <script src="ai-integration.js"></script>
  </head>
  <body>
    <div class="container">
      <h1><PERSON>ra <PERSON>zwi reZhangwa</h1>
      <p>Created by <PERSON><PERSON><PERSON> for Benjamin Music Initiatives</p>
      <form id="loginForm">
        <h2>Login</h2>
        <input type="text" id="username" placeholder="Username" required />
        <input type="password" id="password" placeholder="Password" required />
        <button type="submit">Login</button>
        <p>
          Don't have an account? <a href="#" onclick="showSignUp()">Sign Up</a>
        </p>
      </form>

      <div id="signUpForm" class="hidden">
        <h2>Sign Up</h2>
        <input type="text" id="signupEmail" placeholder="Email" required />
        <input
          type="text"
          id="signupPhone"
          placeholder="Phone Number"
          required
        />
        <button onclick="signUp()">Sign Up</button>
        <p>
          Already have an account? <a href="#" onclick="showLogin()">Login</a>
        </p>
      </div>

      <div id="app" class="hidden">
        <!-- Logout/Exit Button -->
        <div class="logout-container">
          <button onclick="logout()" class="logout-btn" title="Exit App">
            <span class="door-icon">🚪</span>
            <span class="logout-text">Exit</span>
          </button>
        </div>

        <h2>Enter a Word</h2>
        <input type="text" id="wordInput" placeholder="Type a word" />
        <button onclick="breakdownWord()">Basic Breakdown</button>
        <button onclick="enhancedBreakdownWord()">AI Enhanced Breakdown</button>

        <!-- File Upload Section -->
        <div id="fileUploadSection">
          <h3>Upload a Document</h3>
          <input type="file" id="fileInput" accept=".pdf,.txt,.csv,.xlsx" />
          <button onclick="processFile()">Process File</button>
        </div>

        <div id="result">
          <h3>Breakdown Results</h3>
          <p>
            <strong>1. Root Word:</strong>
            <input type="text" id="rootWord" readonly />
          </p>
          <p>
            <strong>2. Prefix:</strong>
            <input type="text" id="prefix" readonly />
          </p>
          <p>
            <strong>3. Suffix:</strong>
            <input type="text" id="suffix" readonly />
          </p>
          <p>
            <strong>4. Syllables:</strong>
            <input type="text" id="syllables" readonly />
          </p>
          <p>
            <strong>5. Origin:</strong>
            <input type="text" id="origin" readonly />
          </p>
          <p>
            <strong>6. Compound:</strong>
            <input type="text" id="compound" readonly />
          </p>
          <p>
            <strong>7. Context:</strong>
            <input type="text" id="context" readonly />
          </p>
          <p>
            <strong>8. Vowels:</strong>
            <input type="text" id="vowels" readonly />
          </p>
          <p>
            <strong>9. Others:</strong>
            <input type="text" id="others" readonly />
          </p>
          <button onclick="makeEditable()">Edit</button>
          <button onclick="saveWord()" id="saveButton" disabled>Save</button>
          <button onclick="addCategory()">+ Add Category</button>
          <button onclick="donate()" class="donate-button">Donate</button>
        </div>

        <!-- Export Options -->
        <div id="exportSection">
          <h3>Export Results</h3>
          <button onclick="exportToCSV()">Export as CSV</button>
          <button onclick="exportToPDF()">Export as PDF</button>
          <button onclick="exportToText()">Export as Text</button>
        </div>

        <!-- AI Word Generation -->
        <div id="aiGenerationSection">
          <h3>AI Shona Word Generation</h3>
          <div class="ai-controls">
            <select id="wordCategory">
              <option value="nouns">Nouns</option>
              <option value="verbs">Verbs</option>
              <option value="adjectives">Adjectives</option>
              <option value="adverbs">Adverbs</option>
              <option value="idioms">Idioms</option>
            </select>
            <input type="number" id="wordCount" min="1" max="10" value="5" placeholder="Number of words">
            <button onclick="generateShonaWords()">Generate Words</button>
          </div>
          <div id="aiGeneratedWords" class="ai-results">
            <!-- AI generated words will appear here -->
          </div>
          <div class="ai-validation">
            <input type="text" id="validateWordInput" placeholder="Enter a Shona word to validate">
            <button onclick="validateShonaWord()">Validate Word</button>
            <div id="validationResult"></div>
          </div>
        </div>

        <!-- New Word Creation Section -->
        <div id="newWordSection">
          <h3>Create New Shona Words</h3>
          <div id="apiStatus" class="api-status">
            <!-- API status will be shown here -->
          </div>
          <div class="new-word-method">
            <h4>Method 1: Combine Existing Words</h4>
            <div class="word-combination">
              <input type="text" id="word1" placeholder="First word (e.g., mhizha)" />
              <input type="text" id="word2" placeholder="Second word (e.g., ruzivo)" />
              <button onclick="combineWords()">Combine Words</button>
            </div>
          </div>

          <div class="new-word-method">
            <h4>Method 2: Create from Description</h4>
            <div class="description-creation">
              <textarea id="conceptDescription" placeholder="Describe the concept you want to create a word for (e.g., 'Technology - the application of scientific knowledge for practical purposes')"></textarea>
              <button onclick="createFromDescription()">Create Word</button>
            </div>
          </div>

          <div id="wordCombinations" class="combination-results">
            <!-- Word combinations will appear here -->
          </div>

          <div class="noun-class-section">
            <h4>Apply Shona Noun Classes (Mipanda)</h4>
            <div id="selectedCombination" class="selected-word">
              <!-- Selected word combination will appear here -->
            </div>
            <div class="noun-class-controls">
              <select id="nounClass">
                <option value="">Select Noun Class</option>
                <option value="1">Class 1 (mu-) - Singular persons</option>
                <option value="2">Class 2 (va-) - Plural of class 1</option>
                <option value="3">Class 3 (mu-) - Singular objects, nature</option>
                <option value="4">Class 4 (mi-) - Plural of class 3</option>
                <option value="5">Class 5 (ri-/Ø-) - Singular body parts, misc.</option>
                <option value="6">Class 6 (ma-) - Plural of class 5, misc.</option>
                <option value="7">Class 7 (chi-) - Singular tools, languages</option>
                <option value="8">Class 8 (zvi-) - Plural of class 7</option>
                <option value="9">Class 9 (i-/N-) - Singular animals, misc.</option>
                <option value="10">Class 10 (dzi-/N-) - Plural of class 9</option>
                <option value="11">Class 11 (ru-) - Singular long/thin objects</option>
                <option value="14">Class 14 (u-) - Abstract, mass nouns</option>
                <option value="15">Class 15 (ku-) - Infinitives, singular actions</option>
              </select>
              <button onclick="applyNounClass()">Apply Class</button>
            </div>
          </div>

          <div id="finalNewWords" class="final-results">
            <!-- Final new words with proper noun classes will appear here -->
          </div>
        </div>

        <!-- Saved Words Section -->
        <div id="savedWordsSection" style="background: rgba(0, 0, 0, 0.7); padding: 20px; border-radius: 8px; margin-top: 20px;">
          <h3 style="color: #ffcc00;">Your Created Words</h3>
          <div id="savedWordsList">
            <!-- Saved words will appear here -->
          </div>
          <button onclick="clearSavedWords()" style="background-color: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 4px; margin-top: 10px;">Clear All</button>
          <button onclick="exportSavedWords()" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; margin-top: 10px; margin-left: 10px;">Export Words</button>
        </div>


      </div>
    </div>

    <!-- Footer Section -->
    <footer>
      <div class="footer-content">
        <p>
          Contact:
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
        <div class="footer-links">
          <div>
            <h4>Company</h4>
            <ul>
              <li><a href="#">About</a></li>
              <li><a href="#">Mobile App</a></li>
              <li><a href="#">Terms & Privacy</a></li>
            </ul>
          </div>
          <div>
            <h4>Social</h4>
            <ul>
              <li><a href="#">Facebook</a></li>
              <li><a href="#">Twitter</a></li>
            </ul>
          </div>
          <div>
            <h4>Help</h4>
            <ul>
              <li><a href="#">Feedback</a></li>
              <li><a href="#">Contact Us</a></li>
              <li><a href="#">DMCA & Copyright</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(0, 0, 0, 0.8); border-radius: 8px;">
        <p style="color: #ffcc00; font-weight: bold; margin: 0; font-size: 1.1em;">
          Created by Anesu Adrian Mupemhi for Benjamin Music Initiatives
        </p>
        <p style="color: white; margin: 5px 0 0 0;">Copyright 2025</p>
      </div>
    </footer>

    <script src="script.js"></script>
  </body>
</html>
