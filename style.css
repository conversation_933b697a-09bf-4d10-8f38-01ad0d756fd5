/* General Styles */
body {
  font-family: Arial, sans-serif;
  background: linear-gradient(135deg, #6a11cb, #2575fc);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  color: white;
}

.container {
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 90%;
  max-width: 800px;
  margin: 20px;
}

.container h1 {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Credit text styling */
.container > p {
  background: rgba(0, 0, 0, 0.8);
  color: #ffcc00;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  font-size: 1.1em;
}

.hidden {
  display: none;
}

/* Input and Button Styles */
input, button {
  padding: 10px;
  margin: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  cursor: pointer;
  background-color: #007bff;
  color: white;
  border: none;
}

button:hover {
  background-color: #0056b3;
}

.donate-button {
  background-color: #28a745;
}

.donate-button:hover {
  background-color: #218838;
}

/* Ensure "Enter a Word" section is visible */
#app h2 {
  color: #333; /* Dark text for better contrast */
  background: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ensure "No file chosen" text is visible */
#fileUploadSection input[type="file"] {
  color: #333; /* Dark text for better contrast */
  background: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
}

/* Breakdown Results Section */
#result {
  margin-top: 20px;
  text-align: left;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  color: #333;
}

#result p {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

#result strong {
  min-width: 100px;
  display: inline-block;
}

#result input {
  flex: 1;
  margin-left: 10px;
}



/* Footer Section */
footer {
  width: 100%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20px 0;
  text-align: center;
  margin-top: auto;
}

footer a {
  color: #ffcc00;
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

.footer-content {
  max-width: 800px;
  margin: 0 auto;
}

.footer-links {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;
}

.footer-links ul {
  list-style-type: none;
  padding: 0;
}

.footer-links h4 {
  margin-bottom: 10px;
}

/* File Upload Section */
#fileUploadSection {
  margin-top: 20px;
}

#fileUploadSection h3 {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Export Section */
#exportSection {
  margin-top: 20px;
}

#exportSection h3 {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Login and Sign-Up Forms */
#loginForm, #signUpForm {
  background: rgba(0, 0, 0, 0.8);
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 2px solid #ffcc00;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

#loginForm h2, #signUpForm h2 {
  color: #ffcc00;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.5em;
}

#loginForm input, #signUpForm input {
  width: 100%;
  padding: 12px;
  margin: 8px 0;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 1em;
  box-sizing: border-box;
}

#loginForm button, #signUpForm button {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  background-color: #ffcc00;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

#loginForm button:hover, #signUpForm button:hover {
  background-color: #ffd633;
}

#loginForm p, #signUpForm p {
  color: white;
  margin-top: 15px;
  text-align: center;
}

#loginForm a, #signUpForm a {
  color: #ffcc00;
  text-decoration: none;
  font-weight: bold;
}

#loginForm a:hover, #signUpForm a:hover {
  text-decoration: underline;
  color: #ffd633;
}

/* Logout/Exit Button */
.logout-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: 2px solid #dc3545;
  padding: 10px 15px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.logout-btn:hover {
  background: rgba(220, 53, 69, 1);
  border-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.door-icon {
  font-size: 1.2em;
  animation: doorSwing 2s ease-in-out infinite;
}

.logout-text {
  font-size: 0.9em;
}

/* Door swing animation */
@keyframes doorSwing {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(-10deg); }
}

.logout-btn:hover .door-icon {
  animation: doorOpen 0.5s ease-in-out;
}

@keyframes doorOpen {
  0% { transform: rotate(0deg); }
  50% { transform: rotate(-15deg) scale(1.1); }
  100% { transform: rotate(0deg); }
}

/* Mobile responsiveness for logout button */
@media (max-width: 768px) {
  .logout-container {
    top: 15px;
    right: 15px;
  }

  .logout-btn {
    padding: 8px 12px;
    font-size: 0.8em;
  }

  .door-icon {
    font-size: 1em;
  }

  .logout-text {
    font-size: 0.8em;
  }
}

/* AI Integration Styles */
#aiGenerationSection {
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  margin-bottom: 20px;
}

#aiGenerationSection h3 {
  color: #ffcc00;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

.ai-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.ai-controls select,
.ai-controls input {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #222;
  color: white;
  flex: 1;
}

.ai-controls button {
  background-color: #ffcc00;
  color: black;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.ai-controls button:hover {
  background-color: #ffd633;
}

.ai-results {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}

.ai-word-card {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  border-left: 3px solid #ffcc00;
}

.ai-word-card h4 {
  color: #ffcc00;
  margin-top: 0;
  margin-bottom: 5px;
}

.ai-word-card p {
  margin: 5px 0;
  color: white;
}

.ai-validation {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.ai-validation input {
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #222;
  color: white;
}

.ai-validation button {
  background-color: #ffcc00;
  color: black;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

#validationResult {
  width: 100%;
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

/* New Word Creation Styles */
#newWordSection {
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  margin-bottom: 20px;
}

#newWordSection h3 {
  color: #ffcc00;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

#newWordSection h4 {
  color: #ffd633;
  margin-bottom: 10px;
  margin-top: 15px;
}

.new-word-method {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.word-combination {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.word-combination input {
  flex: 1;
  min-width: 200px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #222;
  color: white;
}

.description-creation textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #222;
  color: white;
  resize: vertical;
  margin-bottom: 10px;
}

.combination-results {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}

.combination-card {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  border-left: 3px solid #ffcc00;
  cursor: pointer;
  transition: background-color 0.3s;
}

.combination-card:hover {
  background-color: rgba(255, 204, 0, 0.2);
}

.combination-card.selected {
  background-color: rgba(255, 204, 0, 0.3);
  border-left-color: #ffd633;
}

.combination-card h5 {
  color: #ffcc00;
  margin-top: 0;
  margin-bottom: 5px;
}

.combination-card p {
  margin: 3px 0;
  color: white;
  font-size: 0.9em;
}

.noun-class-section {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.selected-word {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  min-height: 50px;
  border: 2px dashed #ffcc00;
}

.noun-class-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.noun-class-controls select {
  flex: 2;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #222;
  color: white;
}

.noun-class-controls button {
  flex: 1;
  background-color: #ffcc00;
  color: black;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.final-results {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
  min-height: 100px;
}

.final-word-card {
  background-color: rgba(40, 167, 69, 0.3);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  border-left: 3px solid #28a745;
}

.final-word-card h5 {
  color: #28a745;
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1.2em;
}

.final-word-card .word-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 10px;
}

.final-word-card .word-details p {
  margin: 2px 0;
  color: white;
  font-size: 0.9em;
}

/* API Status Indicator */
.api-status {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  text-align: center;
}

.api-status.connected {
  background-color: rgba(40, 167, 69, 0.3);
  border-left: 3px solid #28a745;
}

.api-status.fallback {
  background-color: rgba(255, 193, 7, 0.3);
  border-left: 3px solid #ffc107;
}

.api-status.error {
  background-color: rgba(220, 53, 69, 0.3);
  border-left: 3px solid #dc3545;
}

.api-status p {
  margin: 5px 0;
  color: white;
  font-size: 0.9em;
}

/* Saved Words Section */
#savedWordsSection {
  max-height: 400px;
  overflow-y: auto;
}

#savedWordsSection h3 {
  color: #ffcc00;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

.saved-word-item {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 10px;
  border-left: 3px solid #28a745;
  transition: background-color 0.3s;
}

.saved-word-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.saved-word-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 15px;
}

.saved-word-actions button {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: opacity 0.3s;
}

.saved-word-actions button:hover {
  opacity: 0.8;
}

/* Combination card action buttons */
.combination-card button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.combination-card button:hover {
  background-color: #0056b3;
}

.combination-card button:last-child {
  background-color: #6c757d;
}

.combination-card button:last-child:hover {
  background-color: #545b62;
}