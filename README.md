# Dura Mazwi reZhangwa - Shona Word Breakdown App

A web application for breaking down and analyzing Shona words, with AI integration for enhanced word analysis and generation.

## Features

- Basic word breakdown (prefixes, suffixes, syllables, etc.)
- AI-enhanced word analysis using Deepseek API
- Generate new Shona words with AI
- Validate Shona words for authenticity
- File processing (PDF, TXT, CSV, XLSX)
- Export results in various formats
- User authentication

## AI Integration Setup

This application uses the Deepseek API for AI-powered Shona language analysis. Follow these steps to set up the API integration:

### 1. Get a Deepseek API Key

1. Sign up for an account at [Deepseek AI](https://deepseek.ai)
2. Navigate to your account settings or API section
3. Generate a new API key
4. Copy your API key for the next step

### 2. Configure the Application

1. Create a `.env` file in the root directory (use the provided `.env.example` as a template)
2. Add your Deepseek API key:
   ```
   DEEPSEEK_API_KEY=your-actual-deepseek-api-key-here
   ```
3. Open `config.js` and replace the placeholder API key with your actual key:
   ```javascript
   DEEPSEEK_API: {
     API_KEY: 'your-deepseek-api-key-here', // Replace this line
     ...
   }
   ```

### 3. Test the Integration

1. Open the application in your browser
2. Enter a Shona word in the input field
3. Click "AI Enhanced Breakdown" to test the API connection
4. Try generating new Shona words using the AI generation section

## Using AI Features

### Enhanced Word Breakdown

The AI-enhanced breakdown provides more detailed analysis than the basic breakdown, including:
- More accurate root word identification
- Etymology and origin information
- Cultural context
- Dialect identification
- Example usage

### Generating New Shona Words

1. Select a word category (nouns, verbs, adjectives, etc.)
2. Choose how many words to generate (1-10)
3. Click "Generate Words"
4. View the generated words with their meanings and example usage
5. Click "Analyze This Word" on any generated word to see its breakdown

### Validating Shona Words

1. Enter a word in the validation input field
2. Click "Validate Word"
3. View the validation results, including:
   - Whether the word is valid Shona
   - Confidence level
   - Explanation
   - Suggestions for similar valid words (if invalid)

### Creating New Shona Words

The application includes an advanced feature for creating new Shona words using systematic linguistic combination methods:

#### Method 1: Combining Existing Words

1. Enter two Shona words (e.g., "mhizha" and "ruzivo")
2. Click "Combine Words"
3. The AI will create multiple combinations using:
   - Syllable extraction and recombination
   - Morpheme analysis
   - Vowel harmony rules
   - Shona phonological patterns

Example: mhizha (smith) + ruzivo (knowledge) → mhiru, zhavo, ruzha, etc.

#### Method 2: Creating from Description

1. Describe a concept you want to create a word for
2. Click "Create Word"
3. The AI will:
   - Identify related Shona concepts
   - Find existing relevant words
   - Create new combinations
   - Ensure cultural appropriateness

#### Applying Shona Noun Classes (Mipanda)

After creating word combinations:

1. Select your preferred combination
2. Choose the appropriate noun class (1-21)
3. Click "Apply Class"
4. The system will:
   - Add the correct prefix
   - Apply phonological rules
   - Generate agreement patterns
   - Provide usage examples

#### Noun Class Categories:
- **Classes 1-2**: People (mu-/va-)
- **Classes 3-4**: Objects, nature (mu-/mi-)
- **Classes 5-6**: Body parts, misc. (ri-/ma-)
- **Classes 7-8**: Tools, languages (chi-/zvi-)
- **Classes 9-10**: Animals, misc. (i-/dzi-)
- **Class 11**: Long/thin objects (ru-)
- **Classes 12-13**: Diminutives (ka-/tu-)
- **Class 14**: Abstract concepts (u-)
- **Class 15**: Actions, infinitives (ku-)
- **Classes 16-18**: Locatives (pa-/ku-/mu-)

This systematic approach ensures that new words follow proper Shona linguistic patterns and can be integrated naturally into the language.

## Security Notes

- Never commit your API keys to version control
- Use environment variables for sensitive information
- Update the default login credentials in production

## Development

### Project Structure

- `index.html` - Main application HTML
- `style.css` - Application styling
- `script.js` - Core application logic
- `config.js` - Configuration settings
- `ai-integration.js` - AI integration module
- `.env` - Environment variables (not included in repo)

### Adding New AI Features

To add new AI capabilities:
1. Add new methods to the `ShonaAI` class in `ai-integration.js`
2. Create corresponding UI elements in `index.html`
3. Add event handlers in `script.js`
4. Add styling in `style.css`

## Credits

Created by Anesu Adrian Mupemhi for Benjamin Music Initiatives
