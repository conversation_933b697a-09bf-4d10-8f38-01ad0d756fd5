// AI Integration Module for Shona Word Generation and Analysis
// Uses Deepseek API for enhanced word breakdown and generation

class ShonaAI {
  constructor() {
    this.apiKey = CONFIG.DEEPSEEK_API.API_KEY;
    this.baseUrl = CONFIG.DEEPSEEK_API.BASE_URL;
    this.model = CONFIG.DEEPSEEK_API.MODEL;
    this.requestCount = 0;
    this.lastRequestTime = 0;
  }

  // Rate limiting helper
  async rateLimitDelay() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < CONFIG.REQUEST_CONFIG.RATE_LIMIT_DELAY) {
      await new Promise(resolve => 
        setTimeout(resolve, CONFIG.REQUEST_CONFIG.RATE_LIMIT_DELAY - timeSinceLastRequest)
      );
    }
    this.lastRequestTime = Date.now();
  }

  // Clean JSON response from markdown formatting
  cleanJSONResponse(response) {
    if (!response) return response;



    // Remove markdown code blocks - handle both ```json and ``` patterns
    let cleaned = response
      .replace(/```json\s*/gi, '')
      .replace(/```\s*/g, '')
      .replace(/`/g, ''); // Remove any remaining backticks

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();

    // If response starts with text before JSON, try to extract JSON
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }



    return cleaned;
  }

  // Extract JSON from text with more aggressive cleaning
  extractJSONFromText(text) {
    if (!text) return text;

    // Remove all markdown formatting
    let cleaned = text
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/`[^`]*`/g, '') // Remove inline code
      .replace(/\*\*[^*]*\*\*/g, '') // Remove bold text
      .replace(/\*[^*]*\*/g, '') // Remove italic text
      .replace(/#{1,6}\s[^\n]*/g, '') // Remove headers
      .trim();

    // Find JSON object boundaries
    const openBrace = cleaned.indexOf('{');
    const closeBrace = cleaned.lastIndexOf('}');

    if (openBrace !== -1 && closeBrace !== -1 && closeBrace > openBrace) {
      const jsonStr = cleaned.substring(openBrace, closeBrace + 1);

      // Clean up any remaining formatting issues
      return jsonStr
        .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes
        .replace(/[\u2018\u2019]/g, "'") // Replace smart apostrophes
        .replace(/\n\s*/g, ' ') // Replace newlines with spaces
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays
    }

    return cleaned;
  }

  // Make API request to Deepseek
  async makeAPIRequest(prompt, options = {}) {
    if (!this.apiKey || this.apiKey === 'your-deepseek-api-key-here') {
      throw new Error('Please configure your Deepseek API key in config.js');
    }

    await this.rateLimitDelay();

    const requestBody = {
      model: this.model,
      messages: [
        {
          role: "system",
          content: "You are an expert in Shona language linguistics, etymology, and word formation. You understand the structure, prefixes, suffixes, and cultural context of Shona words. Always respond with valid JSON only, no markdown formatting or code blocks."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: options.maxTokens || CONFIG.DEEPSEEK_API.MAX_TOKENS,
      temperature: options.temperature || CONFIG.DEEPSEEK_API.TEMPERATURE
    };

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;

      // Clean the response before returning
      return this.cleanJSONResponse(content);
    } catch (error) {
      console.error('Deepseek API Error:', error);
      throw error;
    }
  }

  // Enhanced word breakdown using AI
  async enhancedWordBreakdown(word) {
    const prompt = `
    Analyze the Shona word "${word}" and provide a detailed breakdown in JSON format:
    {
      "word": "${word}",
      "rootWord": "root without prefixes/suffixes",
      "prefix": "prefix if any",
      "suffix": "suffix if any",
      "syllables": "syllable breakdown",
      "origin": "etymology and origin",
      "compound": "if compound word, show components",
      "context": "cultural/contextual meaning",
      "vowels": "vowel pattern",
      "meaning": "English translation",
      "dialect": "which Shona dialect",
      "wordClass": "noun/verb/adjective etc",
      "examples": ["usage examples in Shona"]
    }
    
    Provide accurate linguistic analysis based on Shona language rules.
    `;

    try {
      const response = await this.makeAPIRequest(prompt);
      return JSON.parse(response);
    } catch (error) {
      console.error('Error in enhanced word breakdown:', error);
      return null;
    }
  }

  // Generate new Shona words
  async generateShonaWords(category, count = 5) {
    const prompt = `
    Generate ${count} authentic Shona words in the category "${category}".
    For each word, provide:
    {
      "words": [
        {
          "shona": "word in Shona",
          "english": "English meaning",
          "pronunciation": "phonetic pronunciation",
          "category": "${category}",
          "example": "example sentence in Shona",
          "exampleTranslation": "English translation of example"
        }
      ]
    }
    
    Ensure words follow proper Shona linguistic patterns and are culturally appropriate.
    `;

    try {
      const response = await this.makeAPIRequest(prompt);
      return JSON.parse(response);
    } catch (error) {
      console.error('Error generating Shona words:', error);
      return null;
    }
  }

  // Get word suggestions based on context
  async getWordSuggestions(context, wordType = 'any') {
    const prompt = `
    Suggest 10 Shona words related to the context: "${context}"
    Word type preference: ${wordType}
    
    Return as JSON:
    {
      "suggestions": [
        {
          "word": "Shona word",
          "meaning": "English meaning",
          "relevance": "how it relates to context",
          "type": "noun/verb/adjective etc"
        }
      ]
    }
    `;

    try {
      const response = await this.makeAPIRequest(prompt);
      return JSON.parse(response);
    } catch (error) {
      console.error('Error getting word suggestions:', error);
      return null;
    }
  }

  // Validate if a word is authentic Shona
  async validateShonaWord(word) {
    const prompt = `
    Analyze if "${word}" is an authentic Shona word. Return JSON:
    {
      "isValid": true/false,
      "confidence": "percentage confidence",
      "explanation": "why it is or isn't valid Shona",
      "suggestions": ["similar valid words if invalid"]
    }
    `;

    try {
      const response = await this.makeAPIRequest(prompt);
      return JSON.parse(response);
    } catch (error) {
      console.error('Error validating Shona word:', error);
      return null;
    }
  }

  // Create word combinations using systematic approach
  async createWordCombinations(word1, word2) {
    const prompt = `
    Create new Shona words by combining "${word1}" and "${word2}" using systematic linguistic combination methods.

    CRITICAL SHONA PHONOLOGICAL RULES:
    1. Shona is vowel-based - every syllable MUST end with a vowel (a, e, i, o, u)
    2. Consonant clusters like "ch", "ng", "zh", "mh", "bh", "vh", "nj", "gw" MUST be followed by vowels
    3. Examples: "cha", "che", "chi", "cho", "chu" (NOT just "ch")
    4. Examples: "nga", "nge", "ngi", "ngo", "ngu" (NOT just "ng")
    5. Examples: "zha", "zhe", "zhi", "zho", "zhu" (NOT just "zh")
    6. Every word must be pronounceable with complete syllables

    Syllable Structure Rules:
    - CV (consonant + vowel): ma, ru, zi, vo
    - V (vowel only): a, e, i, o, u
    - Never end with consonants alone

    SYSTEMATIC COMBINATION ANALYSIS:

    For mhizha (mhi-zha) + mangwa (ma-ngwa):

    STEP 1 - Two-syllable combinations (PRIORITY):
    Each new word uses ONE syllable from each original word:
    - 4 pairs × 2 orders = 8 total combinations

    Syllable pairs:
    1. mhi + ma = mhima, mamhi
    2. mhi + ngwa = mhingwa, ngwamhi
    3. zha + ma = zhama, mazha
    4. zha + ngwa = zhangwa, ngwazha

    STEP 2 - Three-syllable combinations (if needed):
    Only after establishing two-syllable base, create extended forms:
    - Use 2 syllables from one word + 1 from other
    - Example: mhi + ma + zha = mhimazha

    Break words into proper syllables (consonant clusters + vowel = 1 syllable):
    - mhizha = mhi-zha (2 syllables: "mh" + "i" = mhi, "zh" + "a" = zha)
    - ruzivo = ru-zi-vo (3 syllables)
    - chingwa = chi-ngwa (2 syllables: "ch" + "i" = chi, "ngw" + "a" = ngwa)
    - nguva = ngu-va (2 syllables: "ng" + "u" = ngu, "v" + "a" = va)
    - mangwa = ma-ngwa (2 syllables: "m" + "a" = ma, "ngw" + "a" = ngwa)

    COMBINATION RULES:
    - Use 2 syllables from each word (no repeating vowels from same word)
    - If one word has 3+ syllables, can combine 2 from one + 3 from other
    - Each syllable must end with vowel (a,e,i,o,u)

    Return only valid JSON without any markdown formatting:
    {
      "originalWords": ["${word1}", "${word2}"],
      "combinations": [
        {
          "newWord": "combined word with proper vowel endings",
          "formation": "syllable breakdown showing combination",
          "meaning": "suggested meaning",
          "phonetic": "pronunciation guide",
          "viability": 8,
          "syllables": "syllable breakdown (e.g., mhi-ru-vo)"
        }
      ]
    }

    Generate at least 8-12 combinations following proper Shona vowel-based syllable structure.
    `;

    let response;
    try {
      response = await this.makeAPIRequest(prompt);
      // Try to parse the response
      let parsed;
      try {
        parsed = JSON.parse(response);
      } catch (parseError) {
        // Additional cleaning for stubborn responses
        const extraCleaned = this.extractJSONFromText(response);
        parsed = JSON.parse(extraCleaned);
      }
      return parsed;
    } catch (error) {
      console.error('Error creating word combinations:', error);
      if (error.message.includes('JSON')) {
        console.error('This appears to be a JSON parsing error. The API might be returning formatted text instead of pure JSON.');
        console.error('Raw response was:', response);
      }
      return null;
    }
  }

  // Create word from description/concept
  async createWordFromDescription(description) {
    const prompt = `
    Create a new Shona word for the concept: "${description}"

    Process:
    1. Identify key concepts in the description
    2. Find existing Shona words related to these concepts
    3. Use systematic combination methods to create new words
    4. Apply Shona phonological and morphological rules
    5. Consider cultural appropriateness

    Return JSON:
    {
      "concept": "${description}",
      "relatedWords": ["existing Shona words related to concept"],
      "newWords": [
        {
          "word": "new Shona word",
          "formation": "how it was created",
          "meaning": "detailed meaning",
          "etymology": "word parts and origins",
          "phonetic": "pronunciation",
          "culturalFit": "how well it fits Shona culture 1-10"
        }
      ]
    }

    Generate 5-8 viable new word options.
    `;

    let response;
    try {
      response = await this.makeAPIRequest(prompt);

      // Try to parse the response
      let parsed;
      try {
        parsed = JSON.parse(response);
      } catch (parseError) {

        const extraCleaned = this.extractJSONFromText(response);
        parsed = JSON.parse(extraCleaned);
      }

      return parsed;
    } catch (error) {
      console.error('Error creating word from description:', error);
      if (error.message.includes('JSON')) {
        console.error('Raw response was:', response);
      }
      return null;
    }
  }

  // Apply Shona noun class to a word
  async applyNounClass(word, nounClass) {
    const nounClassData = {
      "1": { prefix: "mu-", meaning: "Singular persons", example: "murume" },
      "2": { prefix: "va-", meaning: "Plural of class 1", example: "varume" },
      "3": { prefix: "mu-", meaning: "Singular objects, nature", example: "muti" },
      "4": { prefix: "mi-", meaning: "Plural of class 3", example: "miti" },
      "5": { prefix: "ri-/Ø-", meaning: "Singular body parts, misc.", example: "ziso" },
      "6": { prefix: "ma-", meaning: "Plural of class 5, misc.", example: "maziso" },
      "7": { prefix: "chi-", meaning: "Singular tools, languages", example: "chigaro" },
      "8": { prefix: "zvi-", meaning: "Plural of class 7", example: "zvigaro" },
      "9": { prefix: "i-/N-", meaning: "Singular animals, misc.", example: "imbwa" },
      "10": { prefix: "dzi-/N-", meaning: "Plural of class 9", example: "dzimbwa" },
      "11": { prefix: "ru-", meaning: "Singular long/thin objects", example: "rutsoka" },
      "14": { prefix: "u-", meaning: "Abstract, mass nouns", example: "uswa" },
      "15": { prefix: "ku-", meaning: "Infinitives, singular actions", example: "kufamba" }
    };

    const classInfo = nounClassData[nounClass];
    if (!classInfo) {
      throw new Error('Invalid noun class');
    }

    const prompt = `
    Apply Shona noun class ${nounClass} (${classInfo.prefix}) to the word "${word}".

    Class ${nounClass} details:
    - Prefix: ${classInfo.prefix}
    - Meaning: ${classInfo.meaning}
    - Example: ${classInfo.example}

    Consider:
    1. Phonological changes when adding prefix
    2. Vowel harmony rules
    3. Consonant assimilation
    4. Semantic appropriateness for this class
    5. Agreement patterns with other words

    Return JSON:
    {
      "originalWord": "${word}",
      "nounClass": ${nounClass},
      "classPrefix": "${classInfo.prefix}",
      "newWord": "word with proper prefix",
      "phonetic": "pronunciation guide",
      "meaning": "meaning with class context",
      "agreement": {
        "demonstrative": "this/that form",
        "possessive": "possessive form",
        "adjective": "adjective agreement pattern"
      },
      "examples": ["usage examples in sentences"]
    }
    `;

    let response;
    try {
      response = await this.makeAPIRequest(prompt);

      // Try to parse the response
      let parsed;
      try {
        parsed = JSON.parse(response);
      } catch (parseError) {

        const extraCleaned = this.extractJSONFromText(response);
        parsed = JSON.parse(extraCleaned);
      }

      return parsed;
    } catch (error) {
      console.error('Error applying noun class:', error);
      if (error.message.includes('JSON')) {
        console.error('Raw response was:', response);
      }
      return null;
    }
  }
}

// Initialize AI integration
const shonaAI = new ShonaAI();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ShonaAI;
} else {
  window.ShonaAI = ShonaAI;
  window.shonaAI = shonaAI;
}
