let savedWords = [];

// Login functionality
document.getElementById('loginForm').addEventListener('submit', function (e) {
  e.preventDefault();
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;

  if (username === "user" && password === "password") {
    document.getElementById('loginForm').classList.add('hidden');
    document.getElementById('app').classList.remove('hidden');
  } else {
    alert("Invalid username or password");
  }
});

// Show sign-up form
function showSignUp() {
  document.getElementById('loginForm').classList.add('hidden');
  document.getElementById('signUpForm').classList.remove('hidden');
}

// Show login form
function showLogin() {
  document.getElementById('signUpForm').classList.add('hidden');
  document.getElementById('loginForm').classList.remove('hidden');
}

// Logout function - return to login page
function logout() {
  if (confirm('Are you sure you want to exit the application?')) {
    // Hide the main app
    document.getElementById('app').classList.add('hidden');

    // Show the login form
    document.getElementById('loginForm').classList.remove('hidden');

    // Clear any form data for security
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';

    // Clear any temporary data
    selectedCombination = null;

    // Clear input fields
    document.getElementById('wordInput').value = '';

    // Show notification
    showNotification('Successfully logged out. Thank you for using Wordbreaker!', 'success');
  }
}

// Sign-up functionality
function signUp() {
  const email = document.getElementById('signupEmail').value;
  const phone = document.getElementById('signupPhone').value;

  if (email && phone) {
    alert("A PIN code has been sent to your phone for confirmation.");
    showLogin();
  } else {
    alert("Please fill in all fields.");
  }
}

// Word breakdown functionality
function breakdownWord() {
  const word = document.getElementById('wordInput').value.trim();
  if (!word) {
    alert("Please enter a word");
    return;
  }

  document.getElementById('rootWord').value = getRootWord(word);
  document.getElementById('prefix').value = getPrefix(word);
  document.getElementById('suffix').value = getSuffix(word);
  document.getElementById('syllables').value = getSyllables(word);
  document.getElementById('origin').value = getOrigin(word);
  document.getElementById('compound').value = isCompoundWord(word);
  document.getElementById('context').value = getContext(word);
  document.getElementById('vowels').value = getVowels(word);
  document.getElementById('others').value = "";
}

// Make fields editable
function makeEditable() {
  const inputs = document.querySelectorAll('#result input');
  inputs.forEach(input => input.removeAttribute('readonly'));
  document.getElementById('saveButton').disabled = false;
}

// Save word
function saveWord() {
  const word = document.getElementById('wordInput').value.trim();
  const breakdown = {
    word,
    rootWord: document.getElementById('rootWord').value,
    prefix: document.getElementById('prefix').value,
    suffix: document.getElementById('suffix').value,
    syllables: document.getElementById('syllables').value,
    origin: document.getElementById('origin').value,
    compound: document.getElementById('compound').value,
    context: document.getElementById('context').value,
    vowels: document.getElementById('vowels').value,
    others: document.getElementById('others').value
  };

  savedWords.push(breakdown);
  savedWords.sort((a, b) => a.word.localeCompare(b.word));
  displaySavedWords();
  resetForm();
}

// Display saved words
function displaySavedWords() {
  const list = document.getElementById('savedWordsList');
  list.innerHTML = savedWords.map((word, index) => `
    <li>
      <span>${word.word}</span>
      <button onclick="editSavedWord(${index})">Edit</button>
      <button onclick="deleteSavedWord(${index})">Delete</button>
    </li>
  `).join('');
}

// Edit saved word
function editSavedWord(index) {
  const word = savedWords[index];
  document.getElementById('wordInput').value = word.word;
  document.getElementById('rootWord').value = word.rootWord;
  document.getElementById('prefix').value = word.prefix;
  document.getElementById('suffix').value = word.suffix;
  document.getElementById('syllables').value = word.syllables;
  document.getElementById('origin').value = word.origin;
  document.getElementById('compound').value = word.compound;
  document.getElementById('context').value = word.context;
  document.getElementById('vowels').value = word.vowels;
  document.getElementById('others').value = word.others;
  makeEditable();
}

// Delete saved word
function deleteSavedWord(index) {
  if (confirm("Are you sure you want to delete this word?")) {
    savedWords.splice(index, 1); // Remove the word from the savedWords array
    displaySavedWords(); // Refresh the list
  }
}

// Reset form
function resetForm() {
  document.getElementById('wordInput').value = '';
  const inputs = document.querySelectorAll('#result input');
  inputs.forEach(input => {
    input.value = '';
    input.setAttribute('readonly', true);
  });
  document.getElementById('saveButton').disabled = true;
}

// Add category
function addCategory() {
  const category = prompt("Enter a new category name:");
  if (category) {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML += `
      <p><strong>${category}:</strong> <input type="text" id="${category.toLowerCase()}" readonly></p>
    `;
  }
}

// Donate functionality
function donate() {
  window.open("https://paypal.me/benjimusic", "_blank");
}

// Function to process uploaded file
function processFile() {
  const fileInput = document.getElementById('fileInput');
  const file = fileInput.files[0];

  if (!file) {
    alert("Please upload a file.");
    return;
  }

  const reader = new FileReader();
  reader.onload = function (e) {
    const content = e.target.result;
    const words = extractWordsFromFile(content, file.type);

    // Clear previous saved words
    savedWords = [];

    // Process each word
    words.forEach(word => {
      document.getElementById('wordInput').value = word;
      breakdownWord(); // Process the word
      saveWord(); // Save the breakdown results
    });

    // Display all saved words
    displaySavedWords();
  };

  if (file.type === 'application/pdf') {
    // Use PDF.js for PDF files
    extractTextFromPDF(file);
  } else {
    // For text-based files (CSV, TXT, Excel)
    reader.readAsText(file);
  }
}

// Function to extract words from file content
function extractWordsFromFile(content, fileType) {
  let words = [];
  if (fileType === 'text/plain' || fileType === 'text/csv') {
    // For text and CSV files
    words = content.split(/[\s,]+/); // Split by spaces or commas
  } else if (fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    // For Excel files (XLSX)
    const workbook = XLSX.read(content, { type: 'binary' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    words = XLSX.utils.sheet_to_json(sheet, { header: 1 }).flat();
  }
  return words.filter(word => word.trim() !== ""); // Remove empty strings
}

// Function to extract text from PDF using PDF.js
function extractTextFromPDF(file) {
  const reader = new FileReader();
  reader.onload = function (e) {
    const pdfData = new Uint8Array(e.target.result);
    pdfjsLib.getDocument({ data: pdfData }).promise.then(pdf => {
      let textContent = "";
      for (let i = 1; i <= pdf.numPages; i++) {
        pdf.getPage(i).then(page => {
          page.getTextContent().then(text => {
            text.items.forEach(item => {
              textContent += item.str + " ";
            });
            if (i === pdf.numPages) {
              const words = textContent.split(/[\s,]+/);
              words.forEach(word => {
                document.getElementById('wordInput').value = word;
                breakdownWord();
              });
            }
          });
        });
      }
    });
  };
  reader.readAsArrayBuffer(file);
}

// Function to export results as CSV
function exportToCSV() {
  const csvContent = "Word,Root Word,Prefix,Suffix,Syllables,Origin,Compound,Context,Vowels,Others\n" +
    savedWords.map(word => `${word.word},${word.rootWord},${word.prefix},${word.suffix},${word.syllables},${word.origin},${word.compound},${word.context},${word.vowels},${word.others}`).join("\n");

  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = "word_breakdown.csv";
  a.click();
}

// Function to export results as PDF
function exportToPDF() {
  const { jsPDF } = window.jspdf;
  const doc = new jsPDF();

  doc.text("Word Breakdown Results", 10, 10);
  savedWords.forEach((word, index) => {
    const y = 20 + (index * 40);
    doc.text(`Word: ${word.word}`, 10, y);
    doc.text(`Root Word: ${word.rootWord}`, 10, y + 5);
    doc.text(`Prefix: ${word.prefix}`, 10, y + 10);
    doc.text(`Suffix: ${word.suffix}`, 10, y + 15);
    doc.text(`Syllables: ${word.syllables}`, 10, y + 20);
    doc.text(`Origin: ${word.origin}`, 10, y + 25);
    doc.text(`Compound: ${word.compound}`, 10, y + 30);
    doc.text(`Context: ${word.context}`, 10, y + 35);
    doc.text(`Vowels: ${word.vowels}`, 10, y + 40);
    doc.text(`Others: ${word.others}`, 10, y + 45);
  });

  doc.save("word_breakdown.pdf");
}

// Function to export results as Text
function exportToText() {
  const textContent = savedWords.map(word => `
Word: ${word.word}
Root Word: ${word.rootWord}
Prefix: ${word.prefix}
Suffix: ${word.suffix}
Syllables: ${word.syllables}
Origin: ${word.origin}
Compound: ${word.compound}
Context: ${word.context}
Vowels: ${word.vowels}
Others: ${word.others}
  `).join("\n");

  const blob = new Blob([textContent], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = "word_breakdown.txt";
  a.click();
}

// Helper functions for word breakdown
function getRootWord(word) { return word.length > 3 ? word.slice(0, -2) : word; }
function getPrefix(word) {
  const prefixes = ["un", "re", "pre", "in"];
  for (const prefix of prefixes) {
    if (word.startsWith(prefix)) return prefix;
  }
  return "None";
}
function getSuffix(word) {
  const suffixes = ["ing", "ness", "ly", "ed"];
  for (const suffix of suffixes) {
    if (word.endsWith(suffix)) return suffix;
  }
  return "None";
}
function getSyllables(word) { return word.match(/[aeiouy]{1,2}/g)?.length || 1; }
function getOrigin(word) { return "Unknown"; }
function isCompoundWord(word) { return word.includes("-") ? "Yes" : "No"; }
function getContext(word) { return "Context depends on usage in a sentence."; }
function getVowels(word) {
  const vowels = word.match(/[aeiou]/gi);
  return vowels ? vowels.join(", ") : "None";
}

// AI Integration Functions

// Enhanced word breakdown using AI
async function enhancedBreakdownWord() {
  const word = document.getElementById('wordInput').value.trim();
  if (!word) {
    alert('Please enter a word first');
    return;
  }

  try {
    // Show loading state
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = '<p>Analyzing word with AI... Please wait.</p>';

    // Get AI-enhanced breakdown
    const aiResult = await shonaAI.enhancedWordBreakdown(word);

    if (aiResult) {
      // Populate fields with AI results
      document.getElementById('rootWord').value = aiResult.rootWord || '';
      document.getElementById('prefix').value = aiResult.prefix || '';
      document.getElementById('suffix').value = aiResult.suffix || '';
      document.getElementById('syllables').value = aiResult.syllables || '';
      document.getElementById('origin').value = aiResult.origin || '';
      document.getElementById('compound').value = aiResult.compound || '';
      document.getElementById('context').value = aiResult.context || '';
      document.getElementById('vowels').value = aiResult.vowels || '';
      document.getElementById('others').value = `Meaning: ${aiResult.meaning || 'N/A'}, Dialect: ${aiResult.dialect || 'N/A'}`;

      // Show the result section
      document.getElementById('result').style.display = 'block';
    } else {
      // Fallback to basic breakdown
      breakdownWord();
    }
  } catch (error) {
    console.error('AI breakdown failed:', error);
    alert('AI analysis failed. Using basic breakdown instead.');
    breakdownWord();
  }
}

// Generate new Shona words using AI
async function generateShonaWords() {
  const category = document.getElementById('wordCategory').value;
  const count = parseInt(document.getElementById('wordCount').value) || 5;
  const resultsDiv = document.getElementById('aiGeneratedWords');

  try {
    resultsDiv.innerHTML = '<p>Generating Shona words... Please wait.</p>';

    const aiResult = await shonaAI.generateShonaWords(category, count);

    if (aiResult && aiResult.words) {
      let html = '<h4>Generated Shona Words:</h4>';
      aiResult.words.forEach(word => {
        html += `
          <div class="ai-word-card">
            <h4>${word.shona}</h4>
            <p><strong>English:</strong> ${word.english}</p>
            <p><strong>Pronunciation:</strong> ${word.pronunciation}</p>
            <p><strong>Example:</strong> ${word.example}</p>
            <p><strong>Translation:</strong> ${word.exampleTranslation}</p>
            <button onclick="analyzeGeneratedWord('${word.shona}')">Analyze This Word</button>
          </div>
        `;
      });
      resultsDiv.innerHTML = html;
    } else {
      resultsDiv.innerHTML = '<p>Failed to generate words. Please check your API configuration.</p>';
    }
  } catch (error) {
    console.error('Word generation failed:', error);
    resultsDiv.innerHTML = '<p>Error generating words. Please try again.</p>';
  }
}

// Validate a Shona word using AI
async function validateShonaWord() {
  const word = document.getElementById('validateWordInput').value.trim();
  const resultDiv = document.getElementById('validationResult');

  if (!word) {
    alert('Please enter a word to validate');
    return;
  }

  try {
    resultDiv.innerHTML = '<p>Validating word... Please wait.</p>';

    const validation = await shonaAI.validateShonaWord(word);

    if (validation) {
      const statusColor = validation.isValid ? '#28a745' : '#dc3545';
      let html = `
        <div style="border-left: 3px solid ${statusColor}; padding: 10px;">
          <h4 style="color: ${statusColor};">${validation.isValid ? 'Valid' : 'Invalid'} Shona Word</h4>
          <p><strong>Confidence:</strong> ${validation.confidence}</p>
          <p><strong>Explanation:</strong> ${validation.explanation}</p>
      `;

      if (validation.suggestions && validation.suggestions.length > 0) {
        html += `<p><strong>Suggestions:</strong> ${validation.suggestions.join(', ')}</p>`;
      }

      html += '</div>';
      resultDiv.innerHTML = html;
    } else {
      resultDiv.innerHTML = '<p>Validation failed. Please try again.</p>';
    }
  } catch (error) {
    console.error('Validation failed:', error);
    resultDiv.innerHTML = '<p>Error validating word. Please try again.</p>';
  }
}

// Analyze a generated word
function analyzeGeneratedWord(word) {
  document.getElementById('wordInput').value = word;
  enhancedBreakdownWord();
}

// New Word Creation Functions
let selectedCombination = null;

// Check and display API status
function checkAPIStatus() {
  const statusDiv = document.getElementById('apiStatus');
  if (!statusDiv) return;

  if (!CONFIG.DEEPSEEK_API.API_KEY || CONFIG.DEEPSEEK_API.API_KEY === 'your-deepseek-api-key-here') {
    statusDiv.className = 'api-status fallback';
    statusDiv.innerHTML = `
      <p><strong>⚠️ Fallback Mode Active</strong></p>
      <p>Using built-in linguistic rules. Configure Deepseek API for enhanced features.</p>
      <p><a href="API_SETUP_GUIDE.md" target="_blank">Setup Guide</a></p>
    `;
  } else {
    statusDiv.className = 'api-status connected';
    statusDiv.innerHTML = `
      <p><strong>✅ AI Enhanced Mode</strong></p>
      <p>Deepseek API configured. Using advanced linguistic analysis.</p>
    `;
  }
}

// Initialize saved words array (declared at top of file)

// Update saved words list display
function updateSavedWordsList() {
  const savedWordsDiv = document.getElementById('savedWordsList');
  if (!savedWordsDiv) return;

  if (!savedWords || savedWords.length === 0) {
    savedWordsDiv.innerHTML = '<p style="color: #ccc; font-style: italic;">No words saved yet. Create and save words to see them here.</p>';
    return;
  }

  let html = `<p style="color: #ccc; margin-bottom: 15px;">You have created ${savedWords.length} word(s):</p>`;

  savedWords.forEach((wordData, index) => {
    const date = new Date(wordData.created).toLocaleDateString();
    html += `
      <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 4px; padding: 12px; margin-bottom: 10px; border-left: 3px solid #28a745;">
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
          <div style="flex: 1;">
            <h5 style="color: #28a745; margin: 0 0 5px 0; font-size: 1.1em;">${wordData.word}</h5>
            <p style="margin: 3px 0; color: white; font-size: 0.9em;"><strong>Meaning:</strong> ${wordData.meaning}</p>
            <p style="margin: 3px 0; color: #ccc; font-size: 0.8em;"><strong>Formation:</strong> ${wordData.formation}</p>
            <p style="margin: 3px 0; color: #ccc; font-size: 0.8em;"><strong>Syllables:</strong> ${wordData.syllables || 'N/A'}</p>
            <p style="margin: 3px 0; color: #ccc; font-size: 0.8em;"><strong>Created:</strong> ${date}</p>
          </div>
          <div style="display: flex; flex-direction: column; gap: 5px; margin-left: 15px;">
            <button onclick="analyzeNewWord('${wordData.word}')" style="background-color: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; cursor: pointer;">Analyze</button>
            <button onclick="addToWordInput('${wordData.word}')" style="background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; cursor: pointer;">Use</button>
            <button onclick="deleteSavedWord(${index})" style="background-color: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; cursor: pointer;">Delete</button>
          </div>
        </div>
      </div>
    `;
  });

  savedWordsDiv.innerHTML = html;
}

// Delete a specific saved word
function deleteSavedWord(index) {
  if (index >= 0 && index < savedWords.length) {
    const word = savedWords[index].word;
    if (confirm(`Are you sure you want to delete "${word}"?`)) {
      savedWords.splice(index, 1);

      // Update localStorage
      try {
        localStorage.setItem('shonaCreatedWords', JSON.stringify(savedWords));
      } catch (e) {
        console.warn('Could not update localStorage:', e);
      }

      updateSavedWordsList();
      showNotification(`"${word}" deleted successfully.`);
    }
  }
}

// Clear all saved words
function clearSavedWords() {
  if (savedWords.length === 0) {
    showNotification('No words to clear.', 'error');
    return;
  }

  if (confirm(`Are you sure you want to delete all ${savedWords.length} saved words? This cannot be undone.`)) {
    savedWords = [];

    // Clear localStorage
    try {
      localStorage.removeItem('shonaCreatedWords');
    } catch (e) {
      console.warn('Could not clear localStorage:', e);
    }

    updateSavedWordsList();
    showNotification('All saved words cleared.');
  }
}

// Export saved words as JSON
function exportSavedWords() {
  if (savedWords.length === 0) {
    showNotification('No words to export.', 'error');
    return;
  }

  const dataStr = JSON.stringify(savedWords, null, 2);
  const dataBlob = new Blob([dataStr], {type: 'application/json'});

  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `shona-created-words-${new Date().toISOString().split('T')[0]}.json`;
  link.click();

  showNotification(`Exported ${savedWords.length} words to JSON file.`);
}

// Keyboard shortcut for logout (Ctrl+L or Cmd+L)
document.addEventListener('keydown', function(e) {
  if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
    e.preventDefault();
    // Only allow logout if app is visible (user is logged in)
    if (!document.getElementById('app').classList.contains('hidden')) {
      logout();
    }
  }
});

// Initialize API status check when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Small delay to ensure CONFIG is loaded
  setTimeout(() => {
    checkAPIStatus();
    loadSavedWords();
  }, 100);
});

// Combine two words to create new words
async function combineWords() {
  const word1 = document.getElementById('word1').value.trim();
  const word2 = document.getElementById('word2').value.trim();
  const resultsDiv = document.getElementById('wordCombinations');

  if (!word1 || !word2) {
    alert('Please enter both words to combine');
    return;
  }

  try {
    resultsDiv.innerHTML = '<p>Creating word combinations... Please wait.</p>';

    // Check if API is configured
    if (!CONFIG.DEEPSEEK_API.API_KEY || CONFIG.DEEPSEEK_API.API_KEY === 'your-deepseek-api-key-here') {
      // Use fallback method
      const combinations = createWordCombinationsFallback(word1, word2);
      displayCombinations(combinations, word1, word2, resultsDiv);
      return;
    }

    const combinations = await shonaAI.createWordCombinations(word1, word2);

    if (combinations && combinations.combinations) {
      let html = `<h4>Word Combinations from "${word1}" + "${word2}":</h4>`;
      combinations.combinations.forEach((combo, index) => {
        html += `
          <div class="combination-card" onclick="selectCombination(${index}, '${combo.newWord}', '${combo.formation}', '${combo.meaning}')">
            <h5>${combo.newWord}</h5>
            <p><strong>Formation:</strong> ${combo.formation}</p>
            <p><strong>Meaning:</strong> ${combo.meaning}</p>
            <p><strong>Pronunciation:</strong> ${combo.phonetic}</p>
            <p><strong>Viability:</strong> ${combo.viability}/10</p>
          </div>
        `;
      });
      resultsDiv.innerHTML = html;
    } else {
      // Fallback to local method
      const combinations = createWordCombinationsFallback(word1, word2);
      displayCombinations(combinations, word1, word2, resultsDiv);
    }
  } catch (error) {
    console.error('Word combination failed:', error);
    // Use fallback method
    const combinations = createWordCombinationsFallback(word1, word2);
    displayCombinations(combinations, word1, word2, resultsDiv);
  }
}

// Create word from description
async function createFromDescription() {
  const description = document.getElementById('conceptDescription').value.trim();
  const resultsDiv = document.getElementById('wordCombinations');

  if (!description) {
    alert('Please enter a description of the concept');
    return;
  }

  try {
    resultsDiv.innerHTML = '<p>Creating words from description... Please wait.</p>';

    // Check if API is configured
    if (!CONFIG.DEEPSEEK_API.API_KEY || CONFIG.DEEPSEEK_API.API_KEY === 'your-deepseek-api-key-here') {
      // Use fallback method
      const result = createWordsFromDescriptionFallback(description);
      displayDescriptionResults(result, description, resultsDiv);
      return;
    }

    const result = await shonaAI.createWordFromDescription(description);

    if (result && result.newWords) {
      let html = `<h4>New Words for: "${description}":</h4>`;
      html += `<p><strong>Related existing words:</strong> ${result.relatedWords.join(', ')}</p>`;

      result.newWords.forEach((word, index) => {
        html += `
          <div class="combination-card" onclick="selectCombination(${index}, '${word.word}', '${word.formation}', '${word.meaning}')">
            <h5>${word.word}</h5>
            <p><strong>Formation:</strong> ${word.formation}</p>
            <p><strong>Meaning:</strong> ${word.meaning}</p>
            <p><strong>Etymology:</strong> ${word.etymology}</p>
            <p><strong>Pronunciation:</strong> ${word.phonetic}</p>
            <p><strong>Cultural Fit:</strong> ${word.culturalFit}/10</p>
          </div>
        `;
      });
      resultsDiv.innerHTML = html;
    } else {
      // Fallback to local method
      const result = createWordsFromDescriptionFallback(description);
      displayDescriptionResults(result, description, resultsDiv);
    }
  } catch (error) {
    console.error('Word creation from description failed:', error);
    // Use fallback method
    const result = createWordsFromDescriptionFallback(description);
    displayDescriptionResults(result, description, resultsDiv);
  }
}

// Select a word combination for noun class application
function selectCombination(index, word, formation, meaning) {
  // Remove previous selection
  document.querySelectorAll('.combination-card').forEach(card => {
    card.classList.remove('selected');
  });

  // Add selection to clicked card
  event.target.closest('.combination-card').classList.add('selected');

  // Store selected combination
  selectedCombination = { word, formation, meaning };

  // Display in selection area
  const selectionDiv = document.getElementById('selectedCombination');
  selectionDiv.innerHTML = `
    <h5>Selected Word: ${word}</h5>
    <p><strong>Formation:</strong> ${formation}</p>
    <p><strong>Meaning:</strong> ${meaning}</p>
    <p>Select a noun class below to apply proper Shona grammar.</p>
  `;
}

// Apply noun class to selected word
async function applyNounClass() {
  if (!selectedCombination) {
    alert('Please select a word combination first');
    return;
  }

  const nounClass = document.getElementById('nounClass').value;
  if (!nounClass) {
    alert('Please select a noun class');
    return;
  }

  const resultsDiv = document.getElementById('finalNewWords');

  try {
    resultsDiv.innerHTML = '<p>Applying noun class... Please wait.</p>';

    const result = await shonaAI.applyNounClass(selectedCombination.word, nounClass);

    if (result) {
      let html = `<h4>Final New Shona Word:</h4>`;
      html += `
        <div class="final-word-card">
          <h5>${result.newWord}</h5>
          <p><strong>Original:</strong> ${result.originalWord}</p>
          <p><strong>Class:</strong> ${result.nounClass} (${result.classPrefix})</p>
          <p><strong>Pronunciation:</strong> ${result.phonetic}</p>
          <p><strong>Meaning:</strong> ${result.meaning}</p>

          <div class="word-details">
            <div>
              <p><strong>Demonstrative:</strong> ${result.agreement.demonstrative}</p>
              <p><strong>Possessive:</strong> ${result.agreement.possessive}</p>
              <p><strong>Adjective Pattern:</strong> ${result.agreement.adjective}</p>
            </div>
            <div>
              <p><strong>Examples:</strong></p>
              ${result.examples.map(ex => `<p>• ${ex}</p>`).join('')}
            </div>
          </div>

          <button onclick="saveNewWord('${result.newWord}', '${result.meaning}', '${selectedCombination.formation}')">Save This Word</button>
          <button onclick="analyzeNewWord('${result.newWord}')">Basic Breakdown</button>
          <button onclick="enhancedAnalyzeNewWord('${result.newWord}')">AI Enhanced Breakdown</button>
          <button onclick="addToWordInput('${result.newWord}')">Use in Main App</button>
        </div>
      `;
      resultsDiv.innerHTML = html;
    } else {
      resultsDiv.innerHTML = '<p>Failed to apply noun class. Please try again.</p>';
    }
  } catch (error) {
    console.error('Noun class application failed:', error);
    resultsDiv.innerHTML = '<p>Error applying noun class. Please try again.</p>';
  }
}

// Save a newly created word with enhanced functionality
function saveNewWord(word, meaning, formation) {
  // Prompt for custom description
  const customDescription = prompt(
    `Add a description for "${word}":\n\nSuggested: ${meaning}`,
    meaning
  );

  if (customDescription === null) return; // User cancelled

  const newWordData = {
    word: word,
    meaning: customDescription || meaning,
    formation: formation,
    created: new Date().toISOString(),
    type: 'created',
    syllables: extractShonaSyllables(word).join('-'),
    phonetic: generatePhonetic(word),
    viability: calculateViability(word)
  };

  // Add to saved words
  if (!savedWords) savedWords = [];
  savedWords.push(newWordData);

  // Save to localStorage for persistence
  try {
    localStorage.setItem('shonaCreatedWords', JSON.stringify(savedWords));
  } catch (e) {
    console.warn('Could not save to localStorage:', e);
  }

  updateSavedWordsList();
  showNotification(`New word "${word}" saved successfully!`);

  // Offer to analyze the word
  if (confirm(`Would you like to analyze "${word}" with basic breakdown?`)) {
    analyzeNewWord(word);
  }
}

// Show notification to user
function showNotification(message, type = 'success') {
  // Create notification element
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: ${type === 'success' ? '#28a745' : '#dc3545'};
    color: white;
    padding: 15px 20px;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    max-width: 300px;
    font-size: 14px;
    animation: slideIn 0.3s ease-out;
  `;
  notification.textContent = message;

  // Add animation styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
  `;
  document.head.appendChild(style);

  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Load saved words from localStorage on page load
function loadSavedWords() {
  try {
    const saved = localStorage.getItem('shonaCreatedWords');
    if (saved) {
      savedWords = JSON.parse(saved);
      updateSavedWordsList();
    }
  } catch (e) {
    console.warn('Could not load saved words:', e);
    savedWords = [];
  }
}

// Analyze a newly created word with basic breakdown
function analyzeNewWord(word) {
  document.getElementById('wordInput').value = word;
  breakdownWord(); // Use basic breakdown

  // Scroll to results
  document.getElementById('result').scrollIntoView({ behavior: 'smooth' });
}

// Analyze a newly created word with AI enhanced breakdown
function enhancedAnalyzeNewWord(word) {
  document.getElementById('wordInput').value = word;
  enhancedBreakdownWord(); // Use AI enhanced breakdown

  // Scroll to results
  document.getElementById('result').scrollIntoView({ behavior: 'smooth' });
}

// Add word to main input for further analysis
function addToWordInput(word) {
  document.getElementById('wordInput').value = word;
  document.getElementById('wordInput').focus();

  // Scroll to main input
  document.getElementById('wordInput').scrollIntoView({ behavior: 'smooth' });

  // Show notification
  showNotification(`"${word}" added to main input. You can now analyze or validate it.`);
}

// Comprehensive word combination function following systematic analysis
function createWordCombinationsFallback(word1, word2) {
  const combinations = [];

  // Extract syllables from each word
  const word1Syllables = extractShonaSyllables(word1);
  const word2Syllables = extractShonaSyllables(word2);

  // STEP 1: Two-syllable combinations (one syllable from each word)
  // This follows the systematic analysis: 4 pairs × 2 orders = 8 combinations

  word1Syllables.forEach((syl1, i) => {
    word2Syllables.forEach((syl2, j) => {
      // Forward combination: word1 + word2
      const combo1 = syl1 + syl2;
      if (isValidShonaWord(combo1)) {
        combinations.push({
          newWord: combo1,
          formation: `${syl1} (from ${word1}) + ${syl2} (from ${word2})`,
          meaning: `Concept combining ${word1} and ${word2}`,
          phonetic: generatePhonetic(combo1),
          viability: calculateViability(combo1),
          syllables: `${syl1}-${syl2}`,
          type: 'two-syllable',
          pattern: `${word1}[${i}] + ${word2}[${j}]`
        });
      }

      // Reverse combination: word2 + word1
      const combo2 = syl2 + syl1;
      if (isValidShonaWord(combo2) && combo2 !== combo1) {
        combinations.push({
          newWord: combo2,
          formation: `${syl2} (from ${word2}) + ${syl1} (from ${word1})`,
          meaning: `Concept combining ${word2} and ${word1}`,
          phonetic: generatePhonetic(combo2),
          viability: calculateViability(combo2),
          syllables: `${syl2}-${syl1}`,
          type: 'two-syllable',
          pattern: `${word2}[${j}] + ${word1}[${i}]`
        });
      }
    });
  });

  // STEP 2: Three-syllable combinations (if either word has 3+ syllables)
  // Only create these after establishing the two-syllable base
  if (word1Syllables.length >= 3 || word2Syllables.length >= 3) {

    // Pattern 1: Two syllables from longer word + one from shorter
    if (word1Syllables.length >= 3) {
      for (let i = 0; i < word1Syllables.length - 1; i++) {
        word2Syllables.forEach((syl2, j) => {
          const syl1a = word1Syllables[i];
          const syl1b = word1Syllables[i + 1];

          // Different arrangements
          const patterns = [
            { combo: syl1a + syl2 + syl1b, formation: `${syl1a}-${syl2}-${syl1b}`, desc: `${word1}[${i},${i+1}] + ${word2}[${j}]` },
            { combo: syl1a + syl1b + syl2, formation: `${syl1a}-${syl1b}-${syl2}`, desc: `${word1}[${i},${i+1}] + ${word2}[${j}]` },
            { combo: syl2 + syl1a + syl1b, formation: `${syl2}-${syl1a}-${syl1b}`, desc: `${word2}[${j}] + ${word1}[${i},${i+1}]` }
          ];

          patterns.forEach(pattern => {
            if (isValidShonaWord(pattern.combo)) {
              combinations.push({
                newWord: pattern.combo,
                formation: `${pattern.formation} (${pattern.desc})`,
                meaning: `Extended concept combining ${word1} and ${word2}`,
                phonetic: generatePhonetic(pattern.combo),
                viability: calculateViability(pattern.combo),
                syllables: pattern.formation,
                type: 'three-syllable',
                pattern: pattern.desc
              });
            }
          });
        });
      }
    }

    // Pattern 2: Two syllables from word2 + one from word1 (if word2 has 3+ syllables)
    if (word2Syllables.length >= 3) {
      for (let j = 0; j < word2Syllables.length - 1; j++) {
        word1Syllables.forEach((syl1, i) => {
          const syl2a = word2Syllables[j];
          const syl2b = word2Syllables[j + 1];

          // Different arrangements
          const patterns = [
            { combo: syl2a + syl1 + syl2b, formation: `${syl2a}-${syl1}-${syl2b}`, desc: `${word2}[${j},${j+1}] + ${word1}[${i}]` },
            { combo: syl2a + syl2b + syl1, formation: `${syl2a}-${syl2b}-${syl1}`, desc: `${word2}[${j},${j+1}] + ${word1}[${i}]` },
            { combo: syl1 + syl2a + syl2b, formation: `${syl1}-${syl2a}-${syl2b}`, desc: `${word1}[${i}] + ${word2}[${j},${j+1}]` }
          ];

          patterns.forEach(pattern => {
            if (isValidShonaWord(pattern.combo)) {
              combinations.push({
                newWord: pattern.combo,
                formation: `${pattern.formation} (${pattern.desc})`,
                meaning: `Extended concept combining ${word1} and ${word2}`,
                phonetic: generatePhonetic(pattern.combo),
                viability: calculateViability(pattern.combo),
                syllables: pattern.formation,
                type: 'three-syllable',
                pattern: pattern.desc
              });
            }
          });
        });
      }
    }
  }

  // Remove duplicates and organize by type
  const uniqueCombinations = combinations.filter((combo, index, self) =>
    index === self.findIndex(c => c.newWord === combo.newWord)
  );

  // Sort: two-syllable combinations first (higher priority), then by viability
  const sortedCombinations = uniqueCombinations.sort((a, b) => {
    if (a.type !== b.type) {
      return a.type === 'two-syllable' ? -1 : 1; // Two-syllable first
    }
    return b.viability - a.viability; // Then by viability
  });



  return {
    originalWords: [word1, word2],
    combinations: sortedCombinations.slice(0, 16) // Show more combinations
  };
}

// Extract morphemes from a word following Shona syllable structure
function extractMorphemes(word) {
  const parts = [];

  // First, break word into proper Shona syllables
  const syllables = extractShonaSyllables(word);

  // Add individual syllables
  syllables.forEach(syllable => {
    if (syllable.length >= 2) {
      parts.push(syllable);
    }
  });

  // Add combinations of syllables
  for (let i = 0; i < syllables.length - 1; i++) {
    const combo = syllables[i] + syllables[i + 1];
    if (combo.length <= 6) {
      parts.push(combo);
    }
  }

  // Add three-syllable combinations for longer words
  if (syllables.length >= 3) {
    for (let i = 0; i < syllables.length - 2; i++) {
      const combo = syllables[i] + syllables[i + 1] + syllables[i + 2];
      if (combo.length <= 8) {
        parts.push(combo);
      }
    }
  }

  // Remove duplicates
  return [...new Set(parts)];
}

// Extract proper Shona syllables from a word
function extractShonaSyllables(word) {
  const syllables = [];
  let i = 0;

  while (i < word.length) {
    let syllable = '';

    // Handle consonant clusters first (they must include the vowel)
    if (i < word.length - 2) {
      const twoChar = word.substring(i, i + 2);

      // Check for two-character consonant clusters + vowel
      if (['ch', 'ng', 'zh', 'mh', 'bh', 'vh', 'nj', 'gw', 'zv', 'sv', 'tw', 'dz'].includes(twoChar.toLowerCase())) {
        // Must include the vowel that follows
        if (i + 2 < word.length && isVowel(word[i + 2])) {
          syllable = twoChar + word[i + 2]; // consonant cluster + vowel
          i += 3;
        } else {
          // Invalid - consonant cluster without vowel, skip
          i += 2;
          continue;
        }
      }
      // Single consonant + vowel
      else if (isConsonant(word[i]) && isVowel(word[i + 1])) {
        syllable = word[i] + word[i + 1];
        i += 2;
      }
      // Vowel only
      else if (isVowel(word[i])) {
        syllable = word[i];
        i += 1;
      }
      // Skip invalid consonants
      else {
        i += 1;
        continue;
      }
    }
    // Handle end of word
    else if (i < word.length - 1) {
      if (isConsonant(word[i]) && isVowel(word[i + 1])) {
        syllable = word[i] + word[i + 1];
        i += 2;
      } else if (isVowel(word[i])) {
        syllable = word[i];
        i += 1;
      } else {
        i += 1;
        continue;
      }
    }
    // Last character
    else {
      if (isVowel(word[i])) {
        syllable = word[i];
      }
      i += 1;
    }

    if (syllable && syllable.length >= 1) {
      syllables.push(syllable);
    }
  }

  return syllables;
}

// Helper functions
function isVowel(char) {
  return 'aeiou'.includes(char.toLowerCase());
}

function isConsonant(char) {
  return /[bcdfghjklmnpqrstvwxyz]/i.test(char);
}

// Ensure word ends with proper vowel
function ensureProperEnding(word) {
  if (!word) return word;

  // If word doesn't end with vowel, add appropriate vowel
  if (!isVowel(word[word.length - 1])) {
    // Choose vowel based on common Shona patterns
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    const lastChar = word[word.length - 1].toLowerCase();

    // Some heuristics for vowel selection
    if (['r', 'l'].includes(lastChar)) return word + 'a';
    if (['n', 'm'].includes(lastChar)) return word + 'a';
    if (['t', 'd'].includes(lastChar)) return word + 'a';
    if (['k', 'g'].includes(lastChar)) return word + 'a';

    // Default to 'a' as it's most common
    return word + 'a';
  }

  return word;
}

// Validate if a word follows proper Shona phonological structure
function isValidShonaWord(word) {
  if (!word || word.length < 2) return false;

  // Must end with a vowel
  if (!isVowel(word[word.length - 1])) return false;

  // Check each syllable
  const syllables = extractShonaSyllables(word);

  for (const syllable of syllables) {
    // Each syllable must end with a vowel
    if (!isVowel(syllable[syllable.length - 1])) return false;

    // Check for invalid consonant clusters
    if (hasInvalidConsonantClusters(syllable)) return false;
  }

  return true;
}

// Check for invalid consonant clusters
function hasInvalidConsonantClusters(syllable) {
  // These patterns are invalid in Shona (consonants without vowels)
  const invalidPatterns = [
    /ch(?![aeiou])/i,  // ch not followed by vowel
    /ng(?![aeiou])/i,  // ng not followed by vowel
    /zh(?![aeiou])/i,  // zh not followed by vowel
    /mh(?![aeiou])/i,  // mh not followed by vowel
    /bh(?![aeiou])/i,  // bh not followed by vowel
    /vh(?![aeiou])/i,  // vh not followed by vowel
    /nj(?![aeiou])/i,  // nj not followed by vowel
    /gw(?![aeiou])/i,  // gw not followed by vowel
    /[bcdfghjklmnpqrstvwxyz]{3,}/i  // Three or more consonants in a row
  ];

  return invalidPatterns.some(pattern => pattern.test(syllable));
}

// Generate phonetic representation
function generatePhonetic(word) {
  // Simple phonetic mapping for Shona
  return word.toLowerCase()
    .replace(/ch/g, 'ʧ')
    .replace(/zh/g, 'ʒ')
    .replace(/ng/g, 'ŋ')
    .replace(/ny/g, 'ɲ');
}

// Calculate viability score based on Shona phonological rules
function calculateViability(word) {
  let score = 5; // Base score

  // Must follow proper Shona structure
  if (!isValidShonaWord(word)) {
    return 1; // Very low score for invalid structure
  }

  // Bonus for ending with vowel (required in Shona)
  if (isVowel(word[word.length - 1])) {
    score += 2;
  }

  // Bonus for proper syllable structure
  const syllables = extractShonaSyllables(word);
  const validSyllables = syllables.filter(syl =>
    isVowel(syl[syl.length - 1]) && !hasInvalidConsonantClusters(syl)
  );

  if (validSyllables.length === syllables.length) {
    score += 2; // All syllables are valid
  }

  // Prefer words with good vowel distribution
  const vowelCount = (word.match(/[aeiou]/gi) || []).length;
  const consonantCount = word.length - vowelCount;
  const vowelRatio = vowelCount / word.length;

  if (vowelRatio >= 0.4 && vowelRatio <= 0.6) {
    score += 1; // Good vowel-consonant balance
  }

  // Prefer optimal word length for Shona
  if (word.length >= 4 && word.length <= 7) {
    score += 1;
  } else if (word.length < 3 || word.length > 8) {
    score -= 2;
  }

  // Bonus for common Shona consonant clusters used correctly
  const shonaPatterns = ['cha', 'che', 'chi', 'cho', 'chu', 'nga', 'nge', 'ngi', 'ngo', 'ngu',
                         'zha', 'zhe', 'zhi', 'zho', 'zhu', 'mha', 'mhe', 'mhi', 'mho', 'mhu'];

  const hasGoodPatterns = shonaPatterns.some(pattern => word.toLowerCase().includes(pattern));
  if (hasGoodPatterns) {
    score += 1;
  }

  // Penalty for difficult pronunciation
  if (word.includes('ii') || word.includes('uu') || word.includes('aa')) {
    score -= 1; // Double vowels can be difficult
  }

  return Math.max(1, Math.min(10, score));
}

// Fallback function for creating words from description
function createWordsFromDescriptionFallback(description) {
  // Simple keyword mapping for common concepts
  const conceptMappings = {
    'technology': ['mhizha', 'ruzivo'], // smith + knowledge
    'computer': ['chigaro', 'ruzivo'], // tool + knowledge
    'internet': ['nzira', 'ruzhinji'], // path + multitude
    'phone': ['chigaro', 'kutaura'], // tool + speaking
    'car': ['chigaro', 'kufamba'], // tool + walking
    'book': ['rugwaro', 'ruzivo'], // writing + knowledge
    'school': ['chikoro', 'ruzivo'], // place + knowledge
    'medicine': ['mushonga', 'utano'], // medicine + health
    'music': ['mimhanzi', 'ruzivo'], // music + knowledge
    'food': ['chikafu', 'kudya'] // food + eating
  };

  // Find relevant base words
  let baseWords = [];
  const lowerDesc = description.toLowerCase();

  // Check for direct matches
  for (const [concept, words] of Object.entries(conceptMappings)) {
    if (lowerDesc.includes(concept)) {
      baseWords = words;
      break;
    }
  }

  // If no direct match, use generic approach
  if (baseWords.length === 0) {
    if (lowerDesc.includes('tool') || lowerDesc.includes('device') || lowerDesc.includes('instrument')) {
      baseWords = ['chigaro', 'ruzivo']; // tool + knowledge
    } else if (lowerDesc.includes('knowledge') || lowerDesc.includes('information') || lowerDesc.includes('learn')) {
      baseWords = ['ruzivo', 'mhizha']; // knowledge + skill
    } else if (lowerDesc.includes('place') || lowerDesc.includes('location') || lowerDesc.includes('area')) {
      baseWords = ['nzvimbo', 'ruzivo']; // place + knowledge
    } else {
      baseWords = ['chinhu', 'ruzivo']; // thing + knowledge
    }
  }

  // Create combinations using the base words
  const combinations = createWordCombinationsFallback(baseWords[0], baseWords[1]);

  return {
    concept: description,
    relatedWords: baseWords,
    newWords: combinations.combinations.slice(0, 6).map(combo => ({
      word: combo.newWord,
      formation: combo.formation,
      meaning: `${description} - ${combo.meaning}`,
      etymology: combo.formation,
      phonetic: combo.phonetic,
      culturalFit: combo.viability
    }))
  };
}

// Display description results helper function
function displayDescriptionResults(result, description, resultsDiv) {
  if (result && result.newWords && result.newWords.length > 0) {
    let html = `<h4>New Words for: "${description}":</h4>`;
    if (!CONFIG.DEEPSEEK_API.API_KEY || CONFIG.DEEPSEEK_API.API_KEY === 'your-deepseek-api-key-here') {
      html += '<p><em>Note: Using fallback method. Configure Deepseek API for enhanced results.</em></p>';
    }
    html += `<p><strong>Related existing words:</strong> ${result.relatedWords.join(', ')}</p>`;

    result.newWords.forEach((word, index) => {
      html += `
        <div class="combination-card" onclick="selectCombination(${index}, '${word.word}', '${word.formation}', '${word.meaning}')">
          <h5>${word.word}</h5>
          <p><strong>Formation:</strong> ${word.formation}</p>
          <p><strong>Meaning:</strong> ${word.meaning}</p>
          <p><strong>Etymology:</strong> ${word.etymology}</p>
          <p><strong>Pronunciation:</strong> ${word.phonetic}</p>
          <p><strong>Cultural Fit:</strong> ${word.culturalFit}/10</p>
        </div>
      `;
    });
    resultsDiv.innerHTML = html;
  } else {
    resultsDiv.innerHTML = '<p>No words could be generated from this description. Please try a different description.</p>';
  }
}

// Display combinations helper function with systematic organization
function displayCombinations(combinations, word1, word2, resultsDiv) {
  if (combinations && combinations.combinations && combinations.combinations.length > 0) {
    let html = `<h4>Systematic Word Combinations: "${word1}" + "${word2}"</h4>`;

    // Show syllable breakdown
    const word1Syllables = extractShonaSyllables(word1);
    const word2Syllables = extractShonaSyllables(word2);

    html += `
      <div style="background-color: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 4px; margin-bottom: 15px;">
        <p><strong>Syllable Analysis:</strong></p>
        <p>• <strong>${word1}</strong>: ${word1Syllables.join('-')} (${word1Syllables.length} syllables)</p>
        <p>• <strong>${word2}</strong>: ${word2Syllables.join('-')} (${word2Syllables.length} syllables)</p>
        <p>• <strong>Expected two-syllable combinations:</strong> ${word1Syllables.length} × ${word2Syllables.length} × 2 orders = ${word1Syllables.length * word2Syllables.length * 2} total</p>
      </div>
    `;

    if (!CONFIG.DEEPSEEK_API.API_KEY || CONFIG.DEEPSEEK_API.API_KEY === 'your-deepseek-api-key-here') {
      html += '<p><em>Note: Using systematic fallback method. Configure Deepseek API for enhanced results.</em></p>';
    }

    // Separate two-syllable and three-syllable combinations
    const twoSyllable = combinations.combinations.filter(c => c.type === 'two-syllable');
    const threeSyllable = combinations.combinations.filter(c => c.type === 'three-syllable');

    // Display two-syllable combinations first (priority)
    if (twoSyllable.length > 0) {
      html += `<h5>🎯 Two-Syllable Combinations (Priority - ${twoSyllable.length} generated):</h5>`;
      twoSyllable.forEach((combo, index) => {
        html += `
          <div class="combination-card" onclick="selectCombination(${index}, '${combo.newWord}', '${combo.formation}', '${combo.meaning}')" style="border-left: 3px solid #007bff;">
            <h5>${combo.newWord} <span style="font-size: 0.8em; color: #666;">(${combo.syllables})</span></h5>
            <p><strong>Formation:</strong> ${combo.formation}</p>
            <p><strong>Pattern:</strong> ${combo.pattern || 'Standard'}</p>
            <p><strong>Meaning:</strong> ${combo.meaning}</p>
            <p><strong>Pronunciation:</strong> ${combo.phonetic}</p>
            <p><strong>Viability:</strong> ${combo.viability}/10</p>
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.2);">
              <button onclick="event.stopPropagation(); analyzeNewWord('${combo.newWord}')" style="margin-right: 5px; padding: 4px 8px; font-size: 0.8em;">Basic Analysis</button>
              <button onclick="event.stopPropagation(); addToWordInput('${combo.newWord}')" style="padding: 4px 8px; font-size: 0.8em;">Use in Main App</button>
            </div>
          </div>
        `;
      });
    }

    // Display three-syllable combinations as extensions
    if (threeSyllable.length > 0) {
      html += `<h5>📈 Three-Syllable Extensions (${threeSyllable.length} generated):</h5>`;
      threeSyllable.forEach((combo, index) => {
        const adjustedIndex = twoSyllable.length + index;
        html += `
          <div class="combination-card" onclick="selectCombination(${adjustedIndex}, '${combo.newWord}', '${combo.formation}', '${combo.meaning}')" style="border-left: 3px solid #ffc107;">
            <h5>${combo.newWord} <span style="font-size: 0.8em; color: #666;">(${combo.syllables})</span></h5>
            <p><strong>Formation:</strong> ${combo.formation}</p>
            <p><strong>Pattern:</strong> ${combo.pattern || 'Extended'}</p>
            <p><strong>Meaning:</strong> ${combo.meaning}</p>
            <p><strong>Pronunciation:</strong> ${combo.phonetic}</p>
            <p><strong>Viability:</strong> ${combo.viability}/10</p>
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.2);">
              <button onclick="event.stopPropagation(); analyzeNewWord('${combo.newWord}')" style="margin-right: 5px; padding: 4px 8px; font-size: 0.8em;">Basic Analysis</button>
              <button onclick="event.stopPropagation(); addToWordInput('${combo.newWord}')" style="padding: 4px 8px; font-size: 0.8em;">Use in Main App</button>
            </div>
          </div>
        `;
      });
    }

    resultsDiv.innerHTML = html;
  } else {
    resultsDiv.innerHTML = '<p>No combinations could be generated. Please try different words.</p>';
  }
}