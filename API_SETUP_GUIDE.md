# Deepseek API Setup Guide

## Quick Start (Test Without API)

The application now includes fallback functionality that works without the API. You can test the new word creation features immediately:

1. Open `index.html` in your browser
2. Log in with the default credentials (user/password)
3. Try the "Create New Shona Words" section
4. The system will use built-in linguistic rules to create combinations

## Setting Up Deepseek API (For Enhanced Features)

### Step 1: Get Your API Key

1. Visit [Deepseek AI](https://platform.deepseek.com/)
2. Sign up for an account or log in
3. Navigate to the API section
4. Generate a new API key
5. Copy your API key (it should start with `sk-`)

### Step 2: Configure the Application

#### Option A: Edit config.js directly
1. Open `config.js` in your text editor
2. Find line 8: `API_KEY: 'your-deepseek-api-key-here',`
3. Replace `'your-deepseek-api-key-here'` with your actual API key
4. Save the file

Example:
```javascript
API_KEY: 'sk-1234567890abcdef1234567890abcdef',
```

#### Option B: Use environment variables (Recommended)
1. Create a `.env` file in your project root
2. Add your API key:
```
DEEPSEEK_API_KEY=sk-1234567890abcdef1234567890abcdef
```
3. Modify `config.js` to read from environment variables (for server-side use)

### Step 3: Test the Integration

1. Refresh your browser
2. Try the "AI Enhanced Breakdown" feature
3. Test the word generation features
4. If you see "Using fallback method" messages, the API isn't configured yet

## API Configuration Options

### Model Settings
You can customize the AI behavior in `config.js`:

```javascript
DEEPSEEK_API: {
  API_KEY: 'your-api-key-here',
  BASE_URL: 'https://api.deepseek.com/v1',
  MODEL: 'deepseek-chat',        // AI model to use
  MAX_TOKENS: 1000,              // Maximum response length
  TEMPERATURE: 0.7               // Creativity level (0-1)
}
```

### Rate Limiting
Adjust request limits to avoid API quota issues:

```javascript
REQUEST_CONFIG: {
  TIMEOUT: 30000,                // 30 seconds timeout
  RETRY_ATTEMPTS: 3,             // Retry failed requests
  RATE_LIMIT_DELAY: 1000         // 1 second between requests
}
```

## Troubleshooting

### Common Issues

1. **"Failed to create words" error**
   - Check if your API key is correctly set
   - Verify your internet connection
   - Check browser console for detailed errors

2. **"Using fallback method" message**
   - This is normal if API isn't configured
   - Fallback provides basic functionality
   - Configure API for enhanced features

3. **API quota exceeded**
   - Check your Deepseek account usage
   - Increase rate limit delay in config
   - Consider upgrading your API plan

### Testing API Connection

Add this test function to your browser console:

```javascript
// Test API connection
async function testAPI() {
  try {
    const result = await shonaAI.makeAPIRequest("Test message");
    console.log("API working:", result);
  } catch (error) {
    console.error("API error:", error);
  }
}
testAPI();
```

## Features Available

### Without API (Fallback Mode)
- Basic word combinations using linguistic rules
- Syllable extraction and recombination
- Phonetic generation
- Viability scoring
- Noun class application (basic)

### With API (Enhanced Mode)
- AI-powered linguistic analysis
- Cultural context consideration
- Advanced etymology
- Improved phonetic accuracy
- Detailed grammatical patterns
- Context-aware word generation

## Security Notes

- Never commit API keys to version control
- Use environment variables in production
- Rotate API keys regularly
- Monitor API usage and costs

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify your API key is valid
3. Test with fallback mode first
4. Check Deepseek API documentation
5. Review the application logs
