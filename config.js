// Configuration file for AI integration
// Note: Never commit API keys to version control

const CONFIG = {
  // Deepseek API Configuration
  DEEPSEEK_API: {
    // Replace with your actual Deepseek API key
    API_KEY: '***********************************',
    BASE_URL: 'https://api.deepseek.com/v1',
    MODEL: 'deepseek-chat', // or your preferred model
    MAX_TOKENS: 1000,
    TEMPERATURE: 0.7
  },

  // AI Integration Settings
  AI_FEATURES: {
    WORD_GENERATION: true,
    WORD_ANALYSIS: true,
    ETYMOLOGY_LOOKUP: true,
    CONTEXT_SUGGESTIONS: true
  },

  // Shona Language Specific Settings
  SHONA_CONFIG: {
    DIALECTS: ['Zezuru', 'Karanga', 'Manyika', 'Ndau', 'Korekore'],
    WORD_CATEGORIES: [
      'Nouns', 'Verbs', 'Adjectives', 'Adverbs',
      'Pronouns', 'Conjunctions', 'Prepositions'
    ],
    COMMON_PREFIXES: ['mu-', 'va-', 'chi-', 'zvi-', 'ma-', 'ku-'],
    COMMON_SUFFIXES: ['-a', '-e', '-i', '-o', '-u', '-wa', '-ka'],

    // Shona Noun Classes (Mipanda)
    NOUN_CLASSES: {
      1: { prefix: 'mu-', meaning: 'Singular persons', example: 'murume', category: 'people' },
      2: { prefix: 'va-', meaning: 'Plural of class 1', example: 'varume', category: 'people' },
      3: { prefix: 'mu-', meaning: 'Singular objects, nature', example: 'muti', category: 'objects' },
      4: { prefix: 'mi-', meaning: 'Plural of class 3', example: 'miti', category: 'objects' },
      5: { prefix: 'ri-/Ø-', meaning: 'Singular body parts, misc.', example: 'ziso', category: 'body_misc' },
      6: { prefix: 'ma-', meaning: 'Plural of class 5, misc.', example: 'maziso', category: 'body_misc' },
      7: { prefix: 'chi-', meaning: 'Singular tools, languages', example: 'chigaro', category: 'tools' },
      8: { prefix: 'zvi-', meaning: 'Plural of class 7', example: 'zvigaro', category: 'tools' },
      9: { prefix: 'i-/N-', meaning: 'Singular animals, misc.', example: 'imbwa', category: 'animals' },
      10: { prefix: 'dzi-/N-', meaning: 'Plural of class 9', example: 'dzimbwa', category: 'animals' },
      11: { prefix: 'ru-', meaning: 'Singular long/thin objects', example: 'rutsoka', category: 'long_objects' },
      12: { prefix: 'ka-', meaning: 'Diminutive singular', example: 'kambwa', category: 'diminutive' },
      13: { prefix: 'tu-', meaning: 'Plural of class 12', example: 'tumbwa', category: 'diminutive' },
      14: { prefix: 'u-', meaning: 'Abstract, mass nouns', example: 'uswa', category: 'abstract' },
      15: { prefix: 'ku-', meaning: 'Infinitives, singular actions', example: 'kufamba', category: 'actions' },
      16: { prefix: 'pa-', meaning: 'Locative (specific place)', example: 'pamba', category: 'location' },
      17: { prefix: 'ku-', meaning: 'Locative (general area)', example: 'kumusha', category: 'location' },
      18: { prefix: 'mu-', meaning: 'Locative (inside)', example: 'mumba', category: 'location' },
      19: { prefix: 'svi-', meaning: 'Diminutive, rare', example: 'svikiro', category: 'rare' },
      20: { prefix: 'u-', meaning: 'Augmentative, rare', example: 'uguru', category: 'rare' },
      21: { prefix: 'zi-', meaning: 'Augmentative, rare', example: 'zimbwa', category: 'rare' }
    },

    // Word formation patterns
    FORMATION_PATTERNS: {
      SYLLABLE_COMBINATIONS: ['CV', 'V'], // Only CV (consonant+vowel) and V (vowel) allowed
      VOWEL_HARMONY: ['a-a', 'e-e', 'i-i', 'o-o', 'u-u'],
      COMMON_MORPHEMES: ['mhi', 'zha', 'ru', 'zi', 'vo', 'ma', 'ngwa', 'chi', 'zvi'],

      // Proper Shona consonant clusters (must be followed by vowels)
      CONSONANT_CLUSTERS: ['ch', 'ng', 'zh', 'mh', 'bh', 'vh', 'nj', 'gw', 'zv', 'sv', 'tw', 'dz', 'ngw', 'nzw'],

      // Valid syllable examples
      VALID_SYLLABLES: [
        'cha', 'che', 'chi', 'cho', 'chu',
        'nga', 'nge', 'ngi', 'ngo', 'ngu',
        'zha', 'zhe', 'zhi', 'zho', 'zhu',
        'mha', 'mhe', 'mhi', 'mho', 'mhu',
        'bha', 'bhe', 'bhi', 'bho', 'bhu',
        'vha', 'vhe', 'vhi', 'vho', 'vhu',
        'nja', 'nje', 'nji', 'njo', 'nju',
        'gwa', 'gwe', 'gwi', 'gwo', 'gwu'
      ],

      // Phonological rules
      RULES: {
        MUST_END_WITH_VOWEL: true,
        NO_CONSONANT_CLUSTERS_WITHOUT_VOWELS: true,
        SYLLABLE_STRUCTURE: 'CV_or_V_only'
      }
    }
  },

  // API Request Settings
  REQUEST_CONFIG: {
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RATE_LIMIT_DELAY: 1000 // 1 second between requests
  }
};

// Export configuration for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CONFIG;
} else {
  window.CONFIG = CONFIG;
}
